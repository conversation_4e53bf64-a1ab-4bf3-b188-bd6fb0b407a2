# GRAG Installation Guide / GRAG安装指南

本指南提供了多种安装GRAG项目依赖的方法，支持Qwen3模型和GPU加速。

## 🚀 快速安装 / Quick Installation

### 方法1: 使用requirements.txt（推荐）

```bash
# 基础安装
pip install -r requirements.txt

# 使用国内镜像源（中国用户推荐）
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 方法2: 使用快速安装脚本

```bash
# 自动检测GPU并安装合适的依赖
python scripts/install_dependencies.py

# 仅安装CPU版本
python scripts/install_dependencies.py --cpu-only

# 使用官方PyPI源
python scripts/install_dependencies.py --no-mirror

# 仅从requirements.txt安装
python scripts/install_dependencies.py --requirements-only
```

## 🔧 高级安装 / Advanced Installation

### 使用高级安装脚本

```bash
# 完整的分步安装（包含错误处理和测试）
python install_dependencies.py

# 指定镜像源
python install_dependencies.py --mirror tsinghua

# 强制CPU安装
python install_dependencies.py --cpu-only

# 跳过Qwen3测试
python install_dependencies.py --skip-qwen3-test
```

## 📦 依赖说明 / Dependencies Overview

### 核心依赖 / Core Dependencies

| 包名 | 版本要求 | 说明 |
|------|----------|------|
| torch | >=2.0.0 | PyTorch深度学习框架 |
| transformers | >=4.51.0 | HuggingFace Transformers（Qwen3必需） |
| sentence-transformers | >=2.7.0 | 句子嵌入库（Qwen3兼容） |
| datasets | >=2.14.0 | HuggingFace数据集库 |
| accelerate | >=0.24.0 | 分布式训练加速 |

### FAISS版本选择 / FAISS Version Selection

```bash
# CPU版本（默认）
pip install faiss-cpu>=1.10.0

# GPU版本 - CUDA 12.x
pip install faiss-gpu-cu12>=1.11.0

# GPU版本 - CUDA 11.x  
pip install faiss-gpu-cu11>=1.11.0
```

### LangChain生态 / LangChain Ecosystem

| 包名 | 版本要求 | 说明 |
|------|----------|------|
| langchain | >=0.3.0 | LangChain核心库 |
| langgraph | >=0.2.0 | 工作流编排框架 |
| langchain-community | >=0.3.0 | 社区集成 |
| langchain-openai | >=0.2.0 | OpenAI集成 |

## 🎯 特定场景安装 / Scenario-Specific Installation

### 1. 仅CPU环境

```bash
# 方法1: 使用脚本
python scripts/install_dependencies.py --cpu-only

# 方法2: 手动安装
pip install torch>=2.0.0 --index-url https://download.pytorch.org/whl/cpu
pip install faiss-cpu>=1.10.0
pip install transformers>=4.51.0 sentence-transformers>=2.7.0
```

### 2. GPU环境（CUDA 12.x）

```bash
# 自动检测并安装GPU版本
python scripts/install_dependencies.py

# 或手动安装
pip install torch>=2.0.0 --index-url https://download.pytorch.org/whl/cu121
pip install faiss-gpu-cu12>=1.11.0
pip install transformers>=4.51.0 sentence-transformers>=2.7.0
```

### 3. 开发环境

```bash
# 安装所有依赖包括开发工具
pip install -r requirements.txt

# 安装pre-commit钩子
pre-commit install
```

### 4. 生产环境

```bash
# 最小化安装
python scripts/install_dependencies.py --requirements-only

# 跳过开发工具
pip install torch transformers sentence-transformers faiss-cpu langchain streamlit
```

## 🌍 镜像源配置 / Mirror Sources

### 中国用户推荐镜像源

```bash
# 清华大学镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 阿里云镜像
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 中科大镜像
pip install -r requirements.txt -i https://pypi.mirrors.ustc.edu.cn/simple/

# 豆瓣镜像
pip install -r requirements.txt -i https://pypi.douban.com/simple/
```

### 永久配置镜像源

```bash
# 创建pip配置文件
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << EOF
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF
```

## 🧪 安装验证 / Installation Verification

### 基础验证

```bash
# 测试核心包导入
python -c "import torch, transformers, sentence_transformers, faiss; print('✅ Core packages OK')"

# 测试Qwen3兼容性
python scripts/test_qwen3_models.py
```

### GPU验证

```bash
# 检查PyTorch GPU支持
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# 检查FAISS GPU支持
python -c "import faiss; faiss.StandardGpuResources(); print('✅ FAISS GPU OK')"
```

## 🐛 常见问题 / Troubleshooting

### 1. transformers版本过低

```bash
# 错误: Qwen3模型不支持
# 解决: 升级transformers
pip install transformers>=4.51.0 --upgrade
```

### 2. FAISS GPU安装失败

```bash
# 错误: FAISS GPU安装失败
# 解决: 检查CUDA版本并安装对应版本
nvidia-smi  # 查看CUDA版本
pip install faiss-gpu-cu12>=1.11.0  # CUDA 12.x
pip install faiss-gpu-cu11>=1.11.0  # CUDA 11.x
```

### 3. 内存不足

```bash
# 错误: CUDA out of memory
# 解决: 使用CPU版本或减少批次大小
python scripts/install_dependencies.py --cpu-only
```

### 4. 网络连接问题

```bash
# 错误: 下载超时
# 解决: 使用镜像源
python scripts/install_dependencies.py --mirror tsinghua
```

### 5. 编码问题（Windows）

```bash
# 错误: UnicodeDecodeError
# 解决: 设置环境变量
set PYTHONUTF8=1
set PYTHONIOENCODING=utf-8
python scripts/install_dependencies.py
```

## 📋 安装检查清单 / Installation Checklist

- [ ] Python 3.8+ 已安装
- [ ] pip 已升级到最新版本
- [ ] 网络连接正常
- [ ] 足够的磁盘空间（建议5GB+）
- [ ] GPU驱动已安装（如使用GPU）
- [ ] CUDA工具包已安装（如使用GPU）

## 🔄 升级指南 / Upgrade Guide

### 从旧版本升级

```bash
# 1. 备份当前环境
pip freeze > old_requirements.txt

# 2. 升级到Qwen3版本
python scripts/update_config_for_qwen3.py
python scripts/install_dependencies.py

# 3. 重新创建向量存储
python scripts/02_create_vector_store.py
```

## 📞 获取帮助 / Getting Help

如果遇到安装问题：

1. 查看详细错误信息
2. 检查系统要求
3. 尝试使用不同的安装方法
4. 查看项目文档：`QWEN3_UPGRADE_GUIDE.md`
5. 提交Issue到项目仓库

## 🎉 安装完成 / Installation Complete

安装完成后，运行以下命令验证：

```bash
# 测试安装
python scripts/test_qwen3_models.py

# 配置系统
python scripts/update_config_for_qwen3.py

# 启动应用
python scripts/05_run_app.py
```

祝您使用愉快！
