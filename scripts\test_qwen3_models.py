#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3 Models Test Script / Qwen3模型测试脚本

This script tests the Qwen3 embedding and reranker models to ensure they work correctly.
该脚本测试Qwen3嵌入和重排序模型以确保它们正常工作。
"""

import sys
import os
import torch
import numpy as np
from pathlib import Path

# Add project root to path / 将项目根目录添加到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils import get_logger, get_config_manager
from src.retrieval.enhanced_vector_store import MedicalVectorStore
from src.retrieval.reranker import MedicalReranker

logger = get_logger(__name__)

def test_system_requirements():
    """Test system requirements / 测试系统要求"""
    print("🔍 Testing System Requirements")
    print("-" * 40)
    
    # Test Python version / 测试Python版本
    python_version = sys.version_info
    print(f"Python Version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    if python_version < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print("✅ Python version OK")
    
    # Test PyTorch / 测试PyTorch
    try:
        print(f"PyTorch Version: {torch.__version__}")
        print(f"CUDA Available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA Version: {torch.version.cuda}")
            print(f"GPU Count: {torch.cuda.device_count()}")
            print(f"GPU Name: {torch.cuda.get_device_name(0)}")
        print("✅ PyTorch OK")
    except Exception as e:
        print(f"❌ PyTorch error: {e}")
        return False
    
    # Test Transformers / 测试Transformers
    try:
        import transformers
        print(f"Transformers Version: {transformers.__version__}")
        
        # Check if version supports Qwen3 / 检查版本是否支持Qwen3
        version_parts = transformers.__version__.split('.')
        major, minor = int(version_parts[0]), int(version_parts[1])
        if major > 4 or (major == 4 and minor >= 51):
            print("✅ Transformers version supports Qwen3")
        else:
            print("⚠️  Transformers version may not support Qwen3 (requires >=4.51.0)")
    except Exception as e:
        print(f"❌ Transformers error: {e}")
        return False
    
    # Test FAISS / 测试FAISS
    try:
        import faiss
        print(f"FAISS Version: {faiss.__version__}")
        
        # Test GPU support / 测试GPU支持
        try:
            res = faiss.StandardGpuResources()
            print("✅ FAISS GPU support available")
        except:
            print("ℹ️  FAISS GPU support not available (CPU only)")
        
        print("✅ FAISS OK")
    except Exception as e:
        print(f"❌ FAISS error: {e}")
        return False
    
    return True

def test_embedding_model():
    """Test Qwen3 embedding model / 测试Qwen3嵌入模型"""
    print("\n🧠 Testing Qwen3 Embedding Model")
    print("-" * 40)
    
    try:
        # Initialize vector store / 初始化向量存储
        vector_store = MedicalVectorStore()
        
        # Test model loading / 测试模型加载
        print("Loading embedding model...")
        vector_store.load_embedding_model()
        print("✅ Embedding model loaded successfully")
        
        # Test encoding / 测试编码
        test_texts = [
            "什么是高血压？",
            "高血压是一种常见的心血管疾病，指血压持续升高的状态。",
            "糖尿病的症状包括多饮、多尿、多食和体重下降。"
        ]
        
        print("Testing text encoding...")
        embeddings = vector_store.encode_texts(test_texts)
        print(f"✅ Encoded {len(test_texts)} texts")
        print(f"   Embedding shape: {embeddings.shape}")
        print(f"   Embedding dtype: {embeddings.dtype}")
        
        # Test query encoding with instruction / 测试带指令的查询编码
        print("Testing query encoding with instruction...")
        query_embeddings = vector_store.encode_texts(["高血压如何治疗？"], is_query=True)
        print(f"✅ Query encoding successful")
        print(f"   Query embedding shape: {query_embeddings.shape}")
        
        # Test similarity / 测试相似性
        similarity = np.dot(embeddings[0], query_embeddings[0])
        print(f"   Similarity score: {similarity:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Embedding model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reranker_model():
    """Test Qwen3 reranker model / 测试Qwen3重排序模型"""
    print("\n🔄 Testing Qwen3 Reranker Model")
    print("-" * 40)
    
    try:
        # Initialize reranker / 初始化重排序器
        reranker = MedicalReranker()
        
        # Test model loading / 测试模型加载
        print("Loading reranker model...")
        reranker.load_reranker_model()
        print("✅ Reranker model loaded successfully")
        
        # Test reranking / 测试重排序
        query = "高血压的治疗方法有哪些？"
        documents = [
            {
                "text": "高血压的治疗包括药物治疗和生活方式干预。常用的降压药物有ACE抑制剂、ARB、利尿剂等。",
                "score": 0.8
            },
            {
                "text": "糖尿病是一种代谢性疾病，主要特征是血糖升高。",
                "score": 0.6
            },
            {
                "text": "生活方式干预对高血压治疗很重要，包括低盐饮食、规律运动、戒烟限酒等。",
                "score": 0.7
            }
        ]
        
        print(f"Testing reranking with {len(documents)} documents...")
        reranked_docs = reranker.rerank(query, documents, top_k=3)
        print(f"✅ Reranking successful")
        print(f"   Returned {len(reranked_docs)} documents")
        
        # Show reranking results / 显示重排序结果
        print("   Reranking results:")
        for i, doc in enumerate(reranked_docs):
            score = doc.get('rerank_score', 0)
            text_preview = doc.get('text', '')[:50] + "..."
            print(f"     {i+1}. Score: {score:.4f} - {text_preview}")
        
        return True
        
    except Exception as e:
        print(f"❌ Reranker model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vector_store_integration():
    """Test vector store integration / 测试向量存储集成"""
    print("\n🗄️  Testing Vector Store Integration")
    print("-" * 40)
    
    try:
        # Create test documents / 创建测试文档
        test_documents = [
            {
                "text": "高血压是指血压持续升高的疾病，是心血管疾病的重要危险因素。",
                "metadata": {"topic": "高血压", "type": "定义"}
            },
            {
                "text": "高血压的治疗包括药物治疗和非药物治疗。药物治疗主要使用降压药物。",
                "metadata": {"topic": "高血压", "type": "治疗"}
            },
            {
                "text": "糖尿病是一组以高血糖为特征的代谢性疾病。",
                "metadata": {"topic": "糖尿病", "type": "定义"}
            }
        ]
        
        # Initialize vector store / 初始化向量存储
        vector_store = MedicalVectorStore()
        
        # Add documents / 添加文档
        print("Adding test documents to vector store...")
        vector_store.add_documents(test_documents)
        print(f"✅ Added {len(test_documents)} documents")
        
        # Test search / 测试搜索
        query = "高血压如何治疗？"
        print(f"Searching for: {query}")
        results = vector_store.search(query, top_k=2)
        print(f"✅ Search successful, found {len(results)} results")
        
        # Show search results / 显示搜索结果
        for i, (doc, score) in enumerate(results):
            text_preview = doc.get('text', '')[:50] + "..."
            print(f"   {i+1}. Score: {score:.4f} - {text_preview}")
        
        return True
        
    except Exception as e:
        print(f"❌ Vector store integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end():
    """Test end-to-end retrieval and reranking / 测试端到端检索和重排序"""
    print("\n🔄 Testing End-to-End Pipeline")
    print("-" * 40)
    
    try:
        # Create test documents / 创建测试文档
        test_documents = [
            {
                "text": "高血压的药物治疗包括ACE抑制剂、ARB、钙通道阻滞剂、利尿剂和β受体阻滞剂。",
                "metadata": {"topic": "高血压", "type": "药物治疗"}
            },
            {
                "text": "高血压患者应该采用低盐饮食，每日钠摄入量应控制在2.3克以下。",
                "metadata": {"topic": "高血压", "type": "饮食管理"}
            },
            {
                "text": "规律的有氧运动可以有效降低血压，建议每周至少150分钟中等强度运动。",
                "metadata": {"topic": "高血压", "type": "运动治疗"}
            },
            {
                "text": "糖尿病的并发症包括糖尿病肾病、糖尿病视网膜病变等。",
                "metadata": {"topic": "糖尿病", "type": "并发症"}
            }
        ]
        
        # Initialize components / 初始化组件
        vector_store = MedicalVectorStore()
        reranker = MedicalReranker()
        
        # Add documents / 添加文档
        print("Building vector store...")
        vector_store.add_documents(test_documents)
        
        # Search / 搜索
        query = "高血压患者应该如何治疗？"
        print(f"Query: {query}")
        
        # Initial retrieval / 初始检索
        print("Performing initial retrieval...")
        initial_results = vector_store.search(query, top_k=4)
        print(f"Retrieved {len(initial_results)} documents")
        
        # Prepare documents for reranking / 准备重排序文档
        documents_for_reranking = []
        for doc, score in initial_results:
            doc_copy = doc.copy()
            doc_copy["score"] = score
            documents_for_reranking.append(doc_copy)
        
        # Rerank / 重排序
        print("Performing reranking...")
        final_results = reranker.rerank(query, documents_for_reranking, top_k=3)
        
        print(f"✅ End-to-end pipeline successful!")
        print(f"Final results ({len(final_results)} documents):")
        
        for i, doc in enumerate(final_results):
            text_preview = doc.get('text', '')[:60] + "..."
            rerank_score = doc.get('rerank_score', 0)
            original_score = doc.get('score', 0)
            print(f"   {i+1}. Rerank: {rerank_score:.4f}, Original: {original_score:.4f}")
            print(f"      {text_preview}")
        
        return True
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function / 主测试函数"""
    print("🧪 Qwen3 Models Test Suite")
    print("=" * 50)
    
    # Test results / 测试结果
    test_results = {}
    
    # Run tests / 运行测试
    test_results["system"] = test_system_requirements()
    
    if test_results["system"]:
        test_results["embedding"] = test_embedding_model()
        test_results["reranker"] = test_reranker_model()
        test_results["vector_store"] = test_vector_store_integration()
        test_results["end_to_end"] = test_end_to_end()
    else:
        print("\n❌ System requirements not met, skipping model tests")
        test_results.update({
            "embedding": False,
            "reranker": False,
            "vector_store": False,
            "end_to_end": False
        })
    
    # Summary / 总结
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("-" * 20)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Qwen3 models are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        print("💡 Troubleshooting tips:")
        print("   1. Make sure all dependencies are installed")
        print("   2. Check your internet connection for model downloads")
        print("   3. Ensure you have enough GPU memory if using GPU")
        print("   4. Try running: pip install transformers>=4.51.0")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
