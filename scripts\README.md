# GRAG Scripts Directory / GRAG脚本目录

Essential scripts for the GRAG project workflow with Qwen3 support.
GRAG项目工作流的核心脚本，支持Qwen3模型。

## 🚀 Quick Start / 快速开始

### Initial Setup / 初始设置
```bash
# Install dependencies
python install_dependencies.py --quick

# Update configuration for Qwen3
python scripts/update_config_for_qwen3.py

# Test Qwen3 models compatibility
python scripts/test_qwen3_models.py
```

### Core Workflow / 核心工作流
```bash
# Step 1: Download and process data
python scripts/01_download_data.py

# Step 2: Create vector store with Qwen3 embeddings
python scripts/02_create_vector_store.py

# Step 3: Train SFT model (optional)
python scripts/03_train_sft.py

# Step 4: Train GRPO model (optional)
python scripts/04_train_grpo.py

# Step 5: Run the application
python scripts/05_run_app.py
```

## 📋 Script Details / 脚本详情

### Core Workflow Scripts / 核心工作流脚本

| Script | Purpose | Duration | Output |
|--------|---------|----------|---------|
| `01_download_data.py` | 📥 Download & process data | ~30s | Training data, knowledge base |
| `02_create_vector_store.py` | 🗂️ Create FAISS index with Qwen3 | ~5-10min | Vector embeddings |
| `03_train_sft.py` | 🎓 Supervised fine-tuning | Variable | SFT model |
| `04_train_grpo.py` | 🎯 Policy optimization | Variable | GRPO model |
| `05_run_app.py` | 🌐 Launch web interface | Continuous | Streamlit app |

### Qwen3 Support Scripts / Qwen3支持脚本

| Script | Purpose | Duration | Output |
|--------|---------|----------|---------|
| `install_faiss.py` | 🔧 FAISS installation helper | ~2-5min | GPU/CPU FAISS setup |
| `test_qwen3_models.py` | ✅ Test Qwen3 compatibility | ~2-5min | Model validation |
| `update_config_for_qwen3.py` | ⚙️ Update configuration | ~10s | Updated config files |

## Key Features / 主要特性

### 📊 Progress Tracking
- Real-time progress bars
- ETA calculations  
- Batch processing updates
- Memory usage optimization

### 🔄 Error Recovery
- Graceful error handling
- Detailed logging
- Continuation from failures
- Resource cleanup

### 🎯 User-Friendly
- Clear step-by-step execution
- Intuitive naming convention
- Comprehensive documentation
- Visual progress indicators

## 📋 Prerequisites / 前提条件

1. **Environment / 环境**: Python 3.8+ with virtual environment activated
2. **Dependencies / 依赖**: `python install_dependencies.py --quick`
3. **Hardware / 硬件**: 8GB+ RAM recommended, GPU optional for acceleration
4. **Models / 模型**: Qwen3 models will be downloaded automatically

## Troubleshooting / 故障排除

### If a script fails / 如果脚本失败:

1. **Check the logs** for detailed error messages
2. **Verify prerequisites** are met
3. **Restart from the failed step** (scripts are designed to be re-runnable)
4. **Check available resources** (memory, disk space)

### Common solutions / 常见解决方案:

```bash
# Restart virtual environment
deactivate
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# Clear cache if needed
rm -rf scripts/__pycache__

# Check system resources
# Ensure sufficient memory and disk space
```

## ✅ Success Indicators / 成功指标

### Setup Phase / 设置阶段
✅ **Dependencies Installed**: All packages including Qwen3 support
✅ **Config Updated**: Configuration migrated to Qwen3 models
✅ **Models Tested**: Qwen3 embedding and reranker working

### Core Workflow / 核心工作流
✅ **Data Downloaded**: Knowledge base created with medical data
✅ **Vector Store Created**: FAISS index with Qwen3 embeddings
✅ **Models Trained**: SFT and GRPO models optimized (optional)
✅ **App Running**: Web interface accessible at http://localhost:8501

## 🎯 Next Steps / 下一步

After successful execution:

1. **Access the web interface** at http://localhost:8501
2. **Test medical queries** to verify Qwen3 functionality
3. **Customize configurations** in `configs/config.yaml`
4. **Review logs** for any warnings or optimization opportunities

## 📚 Documentation / 文档

- **Installation Guide**: `INSTALL.md`
- **Qwen3 Upgrade Guide**: `QWEN3_UPGRADE_GUIDE.md`
- **Configuration Guide**: `docs/configuration_guide.md`
