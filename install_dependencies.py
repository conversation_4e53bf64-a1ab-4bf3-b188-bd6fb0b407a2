#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GRAG Advanced Dependencies Installation Script / GRAG依赖安装脚本
Updated for Qwen3 Models Support / 已更新支持Qwen3模型

This is the advanced installation script with comprehensive features.
For quick installation, use: python scripts/install_dependencies.py
这是具有全面功能的高级安装脚本。
快速安装请使用: python scripts/install_dependencies.py

Features / 功能:
- Step-by-step installation to avoid build conflicts / 分步安装以避免构建冲突
- Multiple mirror sources for faster downloads / 多个镜像源加速下载
- Automatic GPU detection and FAISS selection / 自动GPU检测和FAISS选择
- Qwen3 models compatibility / Qwen3模型兼容性
- Error handling and fallback mechanisms / 错误处理和回退机制
- Comprehensive testing and validation / 全面测试和验证
"""

import subprocess
import sys
import os
from pathlib import Path
import time
import argparse
import platform

# 镜像源配置
MIRROR_SOURCES = {
    "tsinghua": "https://pypi.tuna.tsinghua.edu.cn/simple",
    "aliyun": "https://mirrors.aliyun.com/pypi/simple/",
    "douban": "https://pypi.douban.com/simple/",
    "ustc": "https://pypi.mirrors.ustc.edu.cn/simple/",
    "official": "https://pypi.org/simple"
}

def test_mirror_speed(mirror_url, timeout=10):
    """测试镜像源速度"""
    try:
        import urllib.request
        start_time = time.time()
        urllib.request.urlopen(mirror_url, timeout=timeout)
        return time.time() - start_time
    except:
        return float('inf')

def select_best_mirror():
    """选择最快的镜像源"""
    print("🔍 Testing mirror sources speed...")
    best_mirror = "official"
    best_time = float('inf')

    for name, url in MIRROR_SOURCES.items():
        print(f"Testing {name}...", end=" ")
        speed = test_mirror_speed(url)
        if speed < best_time:
            best_time = speed
            best_mirror = name
        print(f"{speed:.2f}s" if speed != float('inf') else "Failed")

    print(f"✅ Selected mirror: {best_mirror} ({best_time:.2f}s)")
    return MIRROR_SOURCES[best_mirror]

def detect_gpu_and_cuda():
    """检测GPU和CUDA版本"""
    try:
        # Try to import torch to check CUDA
        import torch
        if torch.cuda.is_available():
            cuda_version = torch.version.cuda
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            return True, cuda_version, gpu_count, gpu_name
        else:
            return False, None, 0, None
    except ImportError:
        # If torch is not installed yet, try to detect CUDA from system
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
            if result.returncode == 0:
                # CUDA is likely available
                return True, "unknown", 1, "NVIDIA GPU"
            else:
                return False, None, 0, None
        except FileNotFoundError:
            return False, None, 0, None

def get_faiss_package(force_cpu=False):
    """根据系统配置选择合适的FAISS包"""
    if force_cpu:
        return "faiss-cpu>=1.10.0"

    has_gpu, cuda_version, gpu_count, gpu_name = detect_gpu_and_cuda()

    if not has_gpu:
        print("🖥️  No GPU detected, using CPU version of FAISS")
        return "faiss-cpu>=1.10.0"

    print(f"🚀 GPU detected: {gpu_name}")
    print(f"   CUDA Version: {cuda_version}")
    print(f"   GPU Count: {gpu_count}")

    # Determine CUDA version for FAISS
    if cuda_version and cuda_version.startswith("12."):
        print("   Using FAISS GPU for CUDA 12.x")
        return "faiss-gpu-cu12>=1.11.0"
    elif cuda_version and cuda_version.startswith("11."):
        print("   Using FAISS GPU for CUDA 11.x")
        return "faiss-gpu-cu11>=1.11.0"
    else:
        print("   Unknown CUDA version, falling back to CPU FAISS")
        return "faiss-cpu>=1.10.0"

def run_pip_install(packages, description="", mirror_url=None, skip_on_error=False):
    """安装指定的包"""
    if isinstance(packages, str):
        packages = [packages]

    print(f"\n{'='*60}")
    print(f"Installing {description}: {', '.join(packages)}")
    print(f"{'='*60}")

    failed_packages = []

    for package in packages:
        try:
            print(f"\n📦 Installing {package}...")

            # 设置环境变量以处理编码问题
            env = os.environ.copy()
            env['PYTHONUTF8'] = '1'
            env['PYTHONIOENCODING'] = 'utf-8'

            cmd = [sys.executable, "-m", "pip", "install"]
            if mirror_url:
                cmd.extend(["-i", mirror_url])
            cmd.append(package)

            subprocess.run(cmd, check=True, capture_output=True, text=True, env=env)
            print(f"✅ Successfully installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}")
            print(f"Error: {e.stderr}")

            # 特殊处理有问题的包
            if "medspacy" in package.lower():
                print("🔄 medspacy has encoding issues, trying alternative...")
                try:
                    # 尝试安装 spacy 和医疗模型作为替代
                    alt_cmd = [sys.executable, "-m", "pip", "install", "spacy"]
                    if mirror_url:
                        alt_cmd.insert(-1, "-i")
                        alt_cmd.insert(-1, mirror_url)
                    subprocess.run(alt_cmd, check=True, capture_output=True, text=True, env=env)
                    print("✅ Installed spacy as alternative to medspacy")
                    continue
                except subprocess.CalledProcessError:
                    print("⚠️  Alternative installation also failed")

            # 如果使用镜像源失败，尝试官方源
            if mirror_url and mirror_url != MIRROR_SOURCES["official"]:
                print("🔄 Retrying with official PyPI...")
                try:
                    subprocess.run([
                        sys.executable, "-m", "pip", "install", package
                    ], check=True, capture_output=True, text=True, env=env)
                    print(f"✅ Successfully installed {package} from official PyPI")
                except subprocess.CalledProcessError:
                    failed_packages.append(package)
                    if not skip_on_error:
                        return False
            else:
                failed_packages.append(package)
                if not skip_on_error:
                    return False

    if failed_packages:
        print(f"\n⚠️  Failed to install: {', '.join(failed_packages)}")
        if skip_on_error:
            print("Continuing with remaining packages...")
        else:
            return False

    return True

def main():
    """主安装流程"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="GRAG Dependencies Installation Script")
    parser.add_argument("--mirror", choices=list(MIRROR_SOURCES.keys()),
                       help="Specify mirror source (default: auto-select)")
    parser.add_argument("--no-mirror", action="store_true",
                       help="Use official PyPI only")
    parser.add_argument("--cpu-only", action="store_true",
                       help="Force CPU-only installation (no GPU packages)")
    parser.add_argument("--skip-qwen3-test", action="store_true",
                       help="Skip Qwen3 models compatibility test")
    args = parser.parse_args()

    print("🚀 GRAG Dependencies Installation")
    print("Updated for Qwen3 Models Support / 已更新支持Qwen3模型")
    print("This script will install dependencies in stages to avoid build conflicts")
    print("支持镜像源加速下载和GPU自动检测")
    print("=" * 60)

    # 确保在正确的目录
    os.chdir(Path(__file__).parent)

    # 检测系统信息
    print("🔍 System Information:")
    print(f"   Python: {sys.version}")
    print(f"   Platform: {platform.system()} {platform.release()}")

    # 检测GPU和CUDA
    has_gpu, cuda_version, gpu_count, gpu_name = detect_gpu_and_cuda()
    if has_gpu:
        print(f"   GPU: {gpu_name} (CUDA {cuda_version})")
    else:
        print("   GPU: Not detected")
    print("=" * 60)

    # 选择镜像源
    if args.no_mirror:
        mirror_url = None
        print("🔧 Using official PyPI only")
    elif args.mirror:
        mirror_url = MIRROR_SOURCES[args.mirror]
        print(f"🔧 Using specified mirror: {args.mirror}")
    else:
        mirror_url = select_best_mirror()

    # 升级基础工具
    print("\n🔧 Upgrading pip and build tools...")
    upgrade_cmd = [sys.executable, "-m", "pip", "install", "--upgrade", "pip", "setuptools", "wheel"]
    if mirror_url:
        upgrade_cmd.insert(-4, "-i")
        upgrade_cmd.insert(-4, mirror_url)

    try:
        subprocess.run(upgrade_cmd, check=True)
        print("✅ Successfully upgraded pip and build tools")
    except subprocess.CalledProcessError as e:
        print("⚠️  Failed to upgrade with mirror, trying official source...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade",
            "pip", "setuptools", "wheel"
        ], check=True)
    
    # 第一阶段：基础依赖
    stage1_packages = [
        "numpy>=1.24.0",
        "pandas>=2.0.0",
        "requests>=2.31.0",
        "tqdm>=4.66.0",
        "python-dotenv>=1.0.0",
        "pyyaml>=6.0.1",
        "loguru>=0.7.2",
        "rich>=13.0.0"
    ]

    if not run_pip_install(stage1_packages, "Basic Dependencies", mirror_url):
        return 1

    # 第二阶段：ML核心库 (Updated for Qwen3)
    stage2_packages = [
        "torch>=2.0.0",
        "transformers>=4.51.0",  # Required for Qwen3 models
        "datasets>=2.14.0",
        "accelerate>=0.24.0",
        "scikit-learn>=1.3.0"
    ]

    if not run_pip_install(stage2_packages, "ML Core Libraries (Qwen3 Compatible)", mirror_url):
        return 1

    # 第三阶段：高级ML库
    stage3_packages = [
        "peft>=0.6.0",
        "trl>=0.7.0"
    ]

    if not run_pip_install(stage3_packages, "Advanced ML Libraries", mirror_url):
        return 1

    # 第四阶段：LangChain生态
    stage4_packages = [
        "langchain>=0.3.0",
        "langchain-community>=0.3.0",
        "langchain-openai>=0.2.0",
        "langchain-anthropic>=0.2.0",
        "langchain-text-splitters>=0.3.0",
        "langchainhub>=0.1.15",
        "langgraph>=0.2.0"
    ]

    if not run_pip_install(stage4_packages, "LangChain Ecosystem", mirror_url):
        return 1

    # 第五阶段：向量数据库和搜索 (Updated for Qwen3 and GPU support)
    # 选择合适的FAISS版本
    faiss_package = get_faiss_package(force_cpu=args.cpu_only)

    stage5_packages = [
        faiss_package,
        "sentence-transformers>=2.7.0",  # Required for Qwen3 models
        "chromadb>=0.4.0",
        "FlagEmbedding>=1.2.0"  # BGE models (backup option)
    ]

    if not run_pip_install(stage5_packages, "Vector DB and Search (Qwen3 Compatible)", mirror_url):
        return 1

    # 第六阶段：Web搜索和处理
    stage6_packages = [
        "tavily-python>=0.3.0",
        "beautifulsoup4>=4.12.0",
        "jieba>=0.42.1",
        "opencc-python-reimplemented>=0.1.7"
    ]

    if not run_pip_install(stage6_packages, "Web Search and Processing", mirror_url):
        return 1

    # 第七阶段：评估工具
    stage7_packages = [
        "rouge-score>=0.1.2",
        "nltk>=3.8.0",
        "bert-score>=0.3.13",
        "ragas>=0.1.0"
    ]

    if not run_pip_install(stage7_packages, "Evaluation Tools", mirror_url):
        return 1

    # 第八阶段：前端
    stage8_packages = [
        "streamlit>=1.28.0",
        "plotly>=5.17.0",
        "altair>=5.1.0",
        "streamlit-chat>=0.1.1",
        "streamlit-option-menu>=0.3.6"
    ]

    if not run_pip_install(stage8_packages, "Frontend", mirror_url):
        return 1

    # 第九阶段：工具和开发（分为两部分）
    stage9a_packages = [
        "wandb>=0.16.0",
        "pytest>=7.4.0",
        "black>=23.9.0",
        "flake8>=6.1.0",
        "pre-commit>=3.5.0",
        "jupyter>=1.0.0"
    ]

    if not run_pip_install(stage9a_packages, "Development Tools", mirror_url):
        return 1

    # 第九阶段B：医疗NLP（可能有问题的包）
    stage9b_packages = [
        "scispacy>=0.5.3",
        "medspacy>=1.0.0"
    ]

    print(f"\n{'='*60}")
    print("Installing Medical NLP packages (may have encoding issues)...")
    print(f"{'='*60}")

    # 使用 skip_on_error=True 来跳过有问题的包
    run_pip_install(stage9b_packages, "Medical NLP", mirror_url, skip_on_error=True)
    
    # 第十阶段：Unsloth (单独安装)
    print(f"\n{'='*60}")
    print("Installing Unsloth (may take longer)...")
    print(f"{'='*60}")

    # 尝试多种方式安装 unsloth
    unsloth_installed = False
    if not unsloth_installed:
        try:
            print("📦 Trying to install unsloth from GitHub...")
            subprocess.run([
                sys.executable, "-m", "pip", "install",
                "git+https://github.com/unslothai/unsloth.git"
            ], check=True, capture_output=True, text=True)
            print("✅ Successfully installed unsloth from GitHub")
            unsloth_installed = True
        except subprocess.CalledProcessError as e:
            print("❌ Failed to install unsloth from GitHub")
            print(f"Error: {e.stderr}")

    if not unsloth_installed:
        print("⚠️  Unsloth installation failed with all methods")
        print("You may need to install it manually later:")
        print("  pip install unsloth")
        print("  or")
        print("  pip install git+https://github.com/unslothai/unsloth.git")

    # 第十一阶段：Qwen3模型兼容性测试
    if not args.skip_qwen3_test:
        print(f"\n{'='*60}")
        print("Testing Qwen3 Models Compatibility...")
        print(f"{'='*60}")

        try:
            print("📦 Testing transformers version for Qwen3 support...")
            import transformers
            version_parts = transformers.__version__.split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])

            if major > 4 or (major == 4 and minor >= 51):
                print(f"✅ Transformers {transformers.__version__} supports Qwen3 models")
            else:
                print(f"⚠️  Transformers {transformers.__version__} may not fully support Qwen3")
                print("   Consider upgrading: pip install transformers>=4.51.0")

            # Test sentence-transformers
            print("📦 Testing sentence-transformers...")
            import sentence_transformers
            print(f"✅ Sentence-transformers {sentence_transformers.__version__} installed")

            # Test FAISS
            print("📦 Testing FAISS installation...")
            import faiss
            print(f"✅ FAISS {faiss.__version__} installed")

            # Test GPU support if available
            if has_gpu and not args.cpu_only:
                try:
                    faiss.StandardGpuResources()
                    print("✅ FAISS GPU support available")
                except:
                    print("ℹ️  FAISS GPU support not available (using CPU)")

            print("✅ All core dependencies are compatible with Qwen3!")

        except ImportError as e:
            print(f"⚠️  Could not test compatibility: {e}")
            print("   This is normal if some packages failed to install")

    print(f"\n{'='*60}")
    print("🎉 Installation completed!")
    print(f"{'='*60}")
    print(f"\nUsed mirror source: {mirror_url or 'Official PyPI'}")
    print(f"FAISS version: {'GPU' if has_gpu and not args.cpu_only else 'CPU'}")
    print("\nNext steps:")
    print("1. Run: python scripts/test_qwen3_models.py  # Test Qwen3 models")
    print("2. Run: python scripts/install_faiss.py     # Alternative FAISS setup")
    print("3. Set up your .env file with API keys")
    print("4. Run: python scripts/02_create_vector_store.py")
    print("5. Run: python scripts/05_run_app.py")

    print("\n📚 Documentation:")
    print("- Qwen3 Upgrade Guide: QWEN3_UPGRADE_GUIDE.md")
    print("- Configuration Guide: docs/configuration_guide.md")

    return 0

if __name__ == "__main__":
    sys.exit(main())
