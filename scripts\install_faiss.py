#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FAISS Installation Script / FAISS安装脚本

This script helps users install the appropriate FAISS version (CPU or GPU) based on their system.
该脚本帮助用户根据系统安装适当的FAISS版本（CPU或GPU）。
"""

import subprocess
import sys
import torch
import platform
from pathlib import Path

def check_cuda_availability():
    """Check if CUDA is available / 检查CUDA是否可用"""
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            cuda_version = torch.version.cuda
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            return True, cuda_version, gpu_count, gpu_name
        else:
            return False, None, 0, None
    except ImportError:
        return False, None, 0, None

def install_package(package_name):
    """Install a package using pip / 使用pip安装包"""
    try:
        print(f"Installing {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ Successfully installed {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}: {e}")
        return False

def uninstall_package(package_name):
    """Uninstall a package using pip / 使用pip卸载包"""
    try:
        print(f"Uninstalling {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", package_name, "-y"])
        print(f"✅ Successfully uninstalled {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to uninstall {package_name}: {e}")
        return False

def check_package_installed(package_name):
    """Check if a package is installed / 检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """Main installation function / 主安装函数"""
    print("🔍 FAISS Installation Helper / FAISS安装助手")
    print("=" * 50)
    
    # Check system information / 检查系统信息
    print(f"🖥️  Platform: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version}")
    
    # Check CUDA availability / 检查CUDA可用性
    cuda_available, cuda_version, gpu_count, gpu_name = check_cuda_availability()
    
    if cuda_available:
        print(f"🚀 CUDA Available: Yes")
        print(f"   CUDA Version: {cuda_version}")
        print(f"   GPU Count: {gpu_count}")
        print(f"   GPU Name: {gpu_name}")
    else:
        print(f"💻 CUDA Available: No")
    
    print("\n" + "=" * 50)
    
    # Check current FAISS installation / 检查当前FAISS安装
    faiss_cpu_installed = check_package_installed("faiss")
    faiss_gpu_installed = False
    
    try:
        import faiss
        # Try to create a GPU resource to check if GPU version is installed
        try:
            res = faiss.StandardGpuResources()
            faiss_gpu_installed = True
        except:
            pass
    except ImportError:
        pass
    
    print("📦 Current FAISS Installation Status:")
    print(f"   FAISS-CPU: {'✅ Installed' if faiss_cpu_installed else '❌ Not installed'}")
    print(f"   FAISS-GPU: {'✅ Installed' if faiss_gpu_installed else '❌ Not installed'}")
    
    # Recommend installation / 推荐安装
    print("\n🎯 Recommendation:")
    
    if cuda_available and cuda_version:
        print("   GPU detected! Recommending FAISS-GPU for better performance.")
        print("   检测到GPU！推荐安装FAISS-GPU以获得更好的性能。")
        
        # Determine CUDA version for package selection / 确定CUDA版本以选择包
        if cuda_version.startswith("12."):
            recommended_package = "faiss-gpu-cu12>=1.11.0"
        elif cuda_version.startswith("11."):
            recommended_package = "faiss-gpu-cu11>=1.11.0"
        else:
            print(f"   ⚠️  Unsupported CUDA version: {cuda_version}")
            print("   Falling back to CPU version.")
            recommended_package = "faiss-cpu>=1.10.0"
    else:
        print("   No GPU detected. Recommending FAISS-CPU.")
        print("   未检测到GPU。推荐安装FAISS-CPU。")
        recommended_package = "faiss-cpu>=1.10.0"
    
    print(f"   Recommended package: {recommended_package}")
    
    # Ask user for confirmation / 询问用户确认
    print("\n" + "=" * 50)
    print("Installation Options / 安装选项:")
    print("1. Install recommended version (推荐版本)")
    print("2. Install CPU version only (仅CPU版本)")
    if cuda_available:
        print("3. Install GPU version (GPU版本)")
    print("4. Skip installation (跳过安装)")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-4): ").strip()
            if choice in ["1", "2", "3", "4"]:
                break
            else:
                print("Invalid choice. Please enter 1, 2, 3, or 4.")
        except KeyboardInterrupt:
            print("\n\n👋 Installation cancelled.")
            return
    
    if choice == "4":
        print("👋 Installation skipped.")
        return
    
    # Uninstall existing FAISS versions / 卸载现有FAISS版本
    if faiss_cpu_installed or faiss_gpu_installed:
        print("\n🗑️  Uninstalling existing FAISS versions...")
        if faiss_cpu_installed:
            uninstall_package("faiss-cpu")
        if faiss_gpu_installed:
            uninstall_package("faiss-gpu")
            uninstall_package("faiss-gpu-cu11")
            uninstall_package("faiss-gpu-cu12")
    
    # Install selected version / 安装选定版本
    print("\n📦 Installing FAISS...")
    
    if choice == "1":
        # Install recommended version / 安装推荐版本
        success = install_package(recommended_package)
    elif choice == "2":
        # Install CPU version / 安装CPU版本
        success = install_package("faiss-cpu>=1.10.0")
    elif choice == "3" and cuda_available:
        # Install GPU version / 安装GPU版本
        if cuda_version.startswith("12."):
            success = install_package("faiss-gpu-cu12>=1.11.0")
        elif cuda_version.startswith("11."):
            success = install_package("faiss-gpu-cu11>=1.11.0")
        else:
            print("❌ Unsupported CUDA version for GPU installation.")
            success = False
    else:
        print("❌ Invalid choice or GPU not available.")
        success = False
    
    if success:
        print("\n✅ FAISS installation completed successfully!")
        print("   You can now use GPU acceleration in your GRAG project.")
        print("   现在您可以在GRAG项目中使用GPU加速了。")
        
        # Test installation / 测试安装
        print("\n🧪 Testing FAISS installation...")
        try:
            import faiss
            print(f"   FAISS version: {faiss.__version__}")
            
            # Test GPU if available / 如果可用则测试GPU
            if cuda_available:
                try:
                    res = faiss.StandardGpuResources()
                    print("   ✅ GPU support: Available")
                except:
                    print("   ❌ GPU support: Not available")
            
            print("   ✅ FAISS is working correctly!")
            
        except ImportError as e:
            print(f"   ❌ FAISS import failed: {e}")
    else:
        print("\n❌ FAISS installation failed.")
        print("   Please check the error messages above and try again.")
        print("   请检查上面的错误信息并重试。")

if __name__ == "__main__":
    main()
