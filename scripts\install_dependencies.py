#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GRAG Quick Dependencies Installation Script / GRAG快速依赖安装脚本
Simplified version for easy installation / 简化版本便于快速安装

This script provides a streamlined installation process for GRAG dependencies
with automatic GPU detection and Qwen3 models support.
该脚本为GRAG依赖提供简化的安装流程，支持自动GPU检测和Qwen3模型。
"""

import subprocess
import sys
import os
from pathlib import Path
import argparse

def run_command(cmd, description="", check=True):
    """运行命令并处理错误"""
    print(f"📦 {description}")
    try:
        result = subprocess.run(cmd, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} - Success")
            return True
        else:
            print(f"❌ {description} - Failed")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"Error: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ {description} - Command not found")
        return False

def detect_gpu():
    """检测GPU可用性"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_with_pip(packages, description="", use_mirror=True):
    """使用pip安装包"""
    if isinstance(packages, str):
        packages = [packages]
    
    mirror_url = "https://pypi.tuna.tsinghua.edu.cn/simple"
    
    for package in packages:
        cmd = [sys.executable, "-m", "pip", "install", package]
        if use_mirror:
            cmd.extend(["-i", mirror_url])
        
        success = run_command(cmd, f"Installing {package}")
        
        # If mirror fails, try official PyPI
        if not success and use_mirror:
            print("🔄 Retrying with official PyPI...")
            cmd = [sys.executable, "-m", "pip", "install", package]
            run_command(cmd, f"Installing {package} (official PyPI)")

def main():
    """主安装函数"""
    parser = argparse.ArgumentParser(description="GRAG Quick Installation Script")
    parser.add_argument("--cpu-only", action="store_true", help="Force CPU-only installation")
    parser.add_argument("--no-mirror", action="store_true", help="Use official PyPI only")
    parser.add_argument("--requirements-only", action="store_true", help="Install from requirements.txt only")
    args = parser.parse_args()

    print("🚀 GRAG Quick Dependencies Installation")
    print("=" * 50)
    
    # Change to project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    # Check if requirements.txt exists
    requirements_file = project_root / "requirements.txt"
    if not requirements_file.exists():
        print("❌ requirements.txt not found!")
        print("Please make sure you're running this script from the project root.")
        return 1
    
    # Detect system capabilities
    has_gpu = detect_gpu() and not args.cpu_only
    print(f"🖥️  GPU Support: {'Yes' if has_gpu else 'No'}")
    
    # Option 1: Install from requirements.txt (recommended)
    if args.requirements_only:
        print("\n📋 Installing from requirements.txt...")
        cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
        if not args.no_mirror:
            cmd.extend(["-i", "https://pypi.tuna.tsinghua.edu.cn/simple"])
        
        success = run_command(cmd, "Installing all dependencies from requirements.txt")
        if not success and not args.no_mirror:
            print("🔄 Retrying with official PyPI...")
            cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
            run_command(cmd, "Installing from requirements.txt (official PyPI)")
    
    else:
        # Option 2: Step-by-step installation
        print("\n🔧 Step-by-step installation...")
        
        # Upgrade pip first
        print("\n1️⃣ Upgrading pip...")
        install_with_pip("pip --upgrade", "Upgrading pip", not args.no_mirror)
        
        # Core dependencies
        print("\n2️⃣ Installing core dependencies...")
        core_packages = [
            "torch>=2.0.0",
            "transformers>=4.51.0",  # Required for Qwen3
            "sentence-transformers>=2.7.0",  # Required for Qwen3
            "datasets>=2.14.0",
            "accelerate>=0.24.0"
        ]
        for package in core_packages:
            install_with_pip(package, f"Core: {package}", not args.no_mirror)
        
        # FAISS installation
        print("\n3️⃣ Installing FAISS...")
        if has_gpu:
            print("🚀 Installing FAISS with GPU support...")
            # Try CUDA 12 first, then CUDA 11, then CPU
            faiss_packages = [
                "faiss-gpu-cu12>=1.11.0",
                "faiss-gpu-cu11>=1.11.0", 
                "faiss-cpu>=1.10.0"
            ]
            faiss_installed = False
            for faiss_pkg in faiss_packages:
                print(f"Trying {faiss_pkg}...")
                cmd = [sys.executable, "-m", "pip", "install", faiss_pkg]
                if not args.no_mirror:
                    cmd.extend(["-i", "https://pypi.tuna.tsinghua.edu.cn/simple"])
                
                if run_command(cmd, f"Installing {faiss_pkg}", check=False):
                    faiss_installed = True
                    break
            
            if not faiss_installed:
                print("⚠️  All FAISS GPU installations failed, falling back to CPU")
                install_with_pip("faiss-cpu>=1.10.0", "FAISS CPU fallback", not args.no_mirror)
        else:
            install_with_pip("faiss-cpu>=1.10.0", "FAISS CPU", not args.no_mirror)
        
        # LangChain ecosystem
        print("\n4️⃣ Installing LangChain ecosystem...")
        langchain_packages = [
            "langchain>=0.3.0",
            "langchain-community>=0.3.0",
            "langgraph>=0.2.0"
        ]
        for package in langchain_packages:
            install_with_pip(package, f"LangChain: {package}", not args.no_mirror)
        
        # Frontend
        print("\n5️⃣ Installing frontend dependencies...")
        frontend_packages = [
            "streamlit>=1.28.0",
            "plotly>=5.17.0"
        ]
        for package in frontend_packages:
            install_with_pip(package, f"Frontend: {package}", not args.no_mirror)
        
        # Utilities
        print("\n6️⃣ Installing utilities...")
        util_packages = [
            "pandas>=2.0.0",
            "numpy>=1.24.0",
            "tqdm>=4.66.0",
            "loguru>=0.7.2",
            "pyyaml>=6.0.1"
        ]
        for package in util_packages:
            install_with_pip(package, f"Utility: {package}", not args.no_mirror)
    
    # Test installation
    print("\n🧪 Testing installation...")
    try:
        import torch
        import transformers
        import sentence_transformers
        import faiss
        import streamlit
        
        print("✅ Core packages imported successfully!")
        print(f"   PyTorch: {torch.__version__}")
        print(f"   Transformers: {transformers.__version__}")
        print(f"   Sentence-Transformers: {sentence_transformers.__version__}")
        print(f"   FAISS: {faiss.__version__}")
        print(f"   Streamlit: {streamlit.__version__}")
        
        # Test Qwen3 compatibility
        version_parts = transformers.__version__.split('.')
        major, minor = int(version_parts[0]), int(version_parts[1])
        if major > 4 or (major == 4 and minor >= 51):
            print("✅ Transformers version supports Qwen3 models!")
        else:
            print("⚠️  Transformers version may not fully support Qwen3")
        
        # Test GPU support
        if has_gpu:
            try:
                faiss.StandardGpuResources()
                print("✅ FAISS GPU support is working!")
            except:
                print("ℹ️  FAISS GPU support not available (using CPU)")
        
    except ImportError as e:
        print(f"⚠️  Some packages may not be installed correctly: {e}")
    
    print("\n🎉 Installation completed!")
    print("=" * 50)
    print("\n📋 Next steps:")
    print("1. Test the installation: python scripts/test_qwen3_models.py")
    print("2. Configure FAISS GPU: python scripts/install_faiss.py")
    print("3. Update config: python scripts/update_config_for_qwen3.py")
    print("4. Create vector store: python scripts/02_create_vector_store.py")
    print("5. Run the app: python scripts/05_run_app.py")
    
    print("\n📚 Documentation:")
    print("- Qwen3 Upgrade Guide: QWEN3_UPGRADE_GUIDE.md")
    print("- Full installation script: install_dependencies.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
