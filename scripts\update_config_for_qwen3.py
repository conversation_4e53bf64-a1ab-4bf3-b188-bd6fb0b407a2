#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration Update Script for Qwen3 Models / Qwen3模型配置更新脚本

This script updates the configuration file to use Qwen3 embedding and reranker models.
该脚本更新配置文件以使用Qwen3嵌入和重排序模型。
"""

import yaml
import torch
from pathlib import Path
import shutil
from datetime import datetime

def backup_config(config_path):
    """Create a backup of the current config / 创建当前配置的备份"""
    backup_path = config_path.parent / f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
    shutil.copy2(config_path, backup_path)
    print(f"✅ Config backed up to: {backup_path}")
    return backup_path

def load_config(config_path):
    """Load configuration from YAML file / 从YAML文件加载配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def save_config(config, config_path):
    """Save configuration to YAML file / 保存配置到YAML文件"""
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)

def detect_gpu_capability():
    """Detect GPU capability / 检测GPU能力"""
    try:
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            cuda_version = torch.version.cuda
            return True, gpu_count, gpu_name, cuda_version
        else:
            return False, 0, None, None
    except:
        return False, 0, None, None

def update_config_for_qwen3(config):
    """Update configuration for Qwen3 models / 为Qwen3模型更新配置"""
    
    # Detect GPU capability / 检测GPU能力
    has_gpu, gpu_count, gpu_name, cuda_version = detect_gpu_capability()
    
    print("🔍 System Information:")
    print(f"   GPU Available: {'Yes' if has_gpu else 'No'}")
    if has_gpu:
        print(f"   GPU Count: {gpu_count}")
        print(f"   GPU Name: {gpu_name}")
        print(f"   CUDA Version: {cuda_version}")
    
    # Update retrieval configuration / 更新检索配置
    if 'retrieval' not in config:
        config['retrieval'] = {}
    
    # Update vector_db configuration / 更新vector_db配置
    if 'vector_db' not in config['retrieval']:
        config['retrieval']['vector_db'] = {}
    
    vector_db_config = config['retrieval']['vector_db']
    vector_db_config.update({
        'type': 'faiss',
        'dimension': 1024,  # Qwen3-Embedding-0.6B dimension
        'index_type': 'IndexFlatIP',
        'save_path': './data/embeddings/faiss_index',
        'creation_batch_size': 4,
        'use_gpu': 'auto',  # auto, cpu, gpu
        'gpu_device': 0
    })
    
    # Update embedding configuration / 更新嵌入配置
    if 'embedding' not in config['retrieval']:
        config['retrieval']['embedding'] = {}
    
    embedding_config = config['retrieval']['embedding']
    embedding_config.update({
        'model_name': 'Qwen/Qwen3-Embedding-0.6B',
        'batch_size': 32,
        'max_length': 8192,  # Qwen3 supports 32K, but 8K is more practical
        'device': 'auto',
        'normalize_embeddings': True,
        'use_instruction': True,
        'instruction_template': '为这个句子生成表示以用于检索相关文章：'
    })
    
    # Update search configuration / 更新搜索配置
    if 'search' not in config['retrieval']:
        config['retrieval']['search'] = {}
    
    search_config = config['retrieval']['search']
    search_config.update({
        'top_k': 10,
        'similarity_threshold': 0.7,
        'adaptive_threshold': True
    })
    
    # Update reranking configuration / 更新重排序配置
    if 'reranking' not in config['retrieval']:
        config['retrieval']['reranking'] = {}
    
    reranking_config = config['retrieval']['reranking']
    reranking_config.update({
        'enabled': True,
        'model_name': 'Qwen/Qwen3-Reranker-0.6B',
        'top_k_rerank': 5,
        'batch_size': 16,
        'use_instruction': True,
        'instruction_template': 'Given a web search query, retrieve relevant passages that answer the query'
    })
    
    return config

def main():
    """Main function / 主函数"""
    print("🔧 Qwen3 Configuration Update Script")
    print("=" * 50)
    
    # Find config file / 查找配置文件
    config_path = Path("configs/config.yaml")
    if not config_path.exists():
        config_path = Path("config.yaml")
        if not config_path.exists():
            print("❌ Configuration file not found!")
            print("   Please make sure you're running this script from the project root directory.")
            return
    
    print(f"📁 Found config file: {config_path}")
    
    # Load current configuration / 加载当前配置
    try:
        config = load_config(config_path)
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return
    
    # Create backup / 创建备份
    backup_path = backup_config(config_path)
    
    # Show current models / 显示当前模型
    print("\n📊 Current Configuration:")
    try:
        current_embedding = config.get('retrieval', {}).get('embedding', {}).get('model_name', 'Not set')
        current_reranker = config.get('retrieval', {}).get('reranking', {}).get('model_name', 'Not set')
        print(f"   Embedding Model: {current_embedding}")
        print(f"   Reranker Model: {current_reranker}")
    except:
        print("   Unable to read current model configuration")
    
    # Ask for confirmation / 询问确认
    print("\n🎯 This script will update your configuration to use:")
    print("   • Qwen/Qwen3-Embedding-0.6B (embedding model)")
    print("   • Qwen/Qwen3-Reranker-0.6B (reranker model)")
    print("   • GPU acceleration support (if available)")
    print("   • Optimized settings for Qwen3 models")
    
    while True:
        try:
            confirm = input("\nDo you want to proceed? (y/n): ").strip().lower()
            if confirm in ['y', 'yes']:
                break
            elif confirm in ['n', 'no']:
                print("👋 Configuration update cancelled.")
                return
            else:
                print("Please enter 'y' or 'n'.")
        except KeyboardInterrupt:
            print("\n\n👋 Configuration update cancelled.")
            return
    
    # Update configuration / 更新配置
    print("\n🔄 Updating configuration...")
    try:
        updated_config = update_config_for_qwen3(config)
        save_config(updated_config, config_path)
        print("✅ Configuration updated successfully!")
    except Exception as e:
        print(f"❌ Failed to update configuration: {e}")
        print(f"   Restoring backup from: {backup_path}")
        shutil.copy2(backup_path, config_path)
        return
    
    # Show updated configuration / 显示更新后的配置
    print("\n📊 Updated Configuration:")
    print(f"   Embedding Model: {updated_config['retrieval']['embedding']['model_name']}")
    print(f"   Reranker Model: {updated_config['retrieval']['reranking']['model_name']}")
    print(f"   GPU Support: {updated_config['retrieval']['vector_db']['use_gpu']}")
    print(f"   Vector Dimension: {updated_config['retrieval']['vector_db']['dimension']}")
    print(f"   Max Length: {updated_config['retrieval']['embedding']['max_length']}")
    
    print("\n✅ Configuration update completed!")
    print("📝 Next steps:")
    print("   1. Install required dependencies: pip install transformers>=4.51.0")
    print("   2. Run FAISS installation script: python scripts/install_faiss.py")
    print("   3. Recreate vector store: python scripts/02_create_vector_store.py")
    print("   4. Test the updated system: python scripts/99_test_model.py")
    
    print(f"\n💾 Backup saved at: {backup_path}")
    print("   You can restore the old configuration if needed.")

if __name__ == "__main__":
    main()
