# Data Configuration / 数据配置
data:
  dataset_name: micha<PERSON><PERSON>zhu/ChatMed_Consult_Dataset  # 数据集名称 / Dataset name (HuggingFace dataset)
  max_samples: 1000  # 最大样本数 / Maximum number of samples to use (-1 for all)
  # Data Preprocessing / 数据预处理
  preprocessing:
    clean_text: true  # 是否清理文本 / Whether to clean text
    max_length: 2048  # 最大文本长度 / Maximum text length
    min_length: 10  # 最小文本长度 / Minimum text length
    remove_duplicates: true  # 是否移除重复项 / Whether to remove duplicates
  # Data Split Configuration / 数据分割配置
  train_split: 0.8  # 训练集比例 / Training set ratio
  val_split: 0.1  # 验证集比例 / Validation set ratio
  test_split: 0.1  # 测试集比例 / Test set ratio
# Environment Configuration / 环境配置
environment:
  device: auto  # 设备选择 / Device selection ("auto", "cpu", "cuda", "mps")
  seed: 3407  # 随机种子 / Random seed for reproducibility
  deterministic: true  # 是否使用确定性算法 / Whether to use deterministic algorithms
  mixed_precision: true  # 是否使用混合精度 / Whether to use mixed precision training
  compile_model: false  # 是否编译模型 / Whether to compile model (PyTorch 2.0+)
# Evaluation Configuration / 评估配置
evaluation:
  # Automatic Metrics / 自动评估指标
  metrics:
  - rouge  # ROUGE分数 / ROUGE scores
  - bleu  # BLEU分数 / BLEU scores
  - bert_score  # BERTScore / BERTScore
  - medical_accuracy  # 医疗准确性 / Medical accuracy
  - safety_score  # 安全性分数 / Safety score
  - relevance_score  # 相关性分数 / Relevance score
  - retrieval_precision  # 检索精确率 / Retrieval precision
  - retrieval_recall  # 检索召回率 / Retrieval recall

  # Medical-Specific Evaluation / 医疗专项评估
  medical_eval:
    enabled: true  # 是否启用医疗评估 / Whether to enable medical evaluation
    criteria:  # 评估标准 / Evaluation criteria
    - symptom_understanding  # 症状理解 / Symptom understanding
    - diagnosis_accuracy  # 诊断准确性 / Diagnosis accuracy
    - treatment_appropriateness  # 治疗适当性 / Treatment appropriateness
    - risk_assessment  # 风险评估 / Risk assessment

  # Human Evaluation / 人工评估
  human_eval:
    enabled: false  # 是否启用人工评估 / Whether to enable human evaluation
    sample_size: 100  # 评估样本数 / Evaluation sample size
    evaluators:  # 评估者类型 / Evaluator types
    - medical_expert  # 医疗专家 / Medical expert
    - general_user  # 普通用户 / General user
# Frontend Configuration / 前端配置
frontend:
  title: GRAG - 医疗问诊智能助手  # 应用标题 / Application title
  subtitle: 基于GRPO优化的医疗RAG系统  # 应用副标题 / Application subtitle
  description: Medical Consultation AI Assistant with GRPO-optimized RAG  # 应用描述 / Application description
  language: zh-CN  # 界面语言 / Interface language
  theme: light  # 主题 / Theme ("light", "dark")
  max_history: 10  # 最大对话历史 / Maximum conversation history
  # Display Options / 显示选项
  show_confidence: true  # 是否显示置信度 / Whether to show confidence scores
  show_sources: true  # 是否显示来源 / Whether to show sources
  show_retrieval_details: true  # 是否显示检索详情 / Whether to show retrieval details
# LangGraph Workflow Configuration / LangGraph工作流配置
langgraph:
  # Workflow Nodes / 工作流节点
  nodes:
  - query_analysis  # 查询分析 / Query analysis
  - retrieval  # 检索 / Retrieval
  - reranking  # 重排序 / Reranking
  - generation  # 生成 / Generation
  - self_correction  # 自我修正 / Self-correction
  - web_search  # 网络搜索 / Web search
  - final_answer  # 最终答案 / Final answer

  # Workflow Edges / 工作流边
  edges:
    # Conditional Edges / 条件边
    conditional_edges:
    # From Query Analysis / 从查询分析
    - from: query_analysis
      conditions:
      - condition: needs_web_search  # 需要网络搜索 / Needs web search
        to: web_search
      - condition: standard_retrieval  # 标准检索 / Standard retrieval
        to: retrieval
    # From Generation / 从生成
    - from: generation
      conditions:
      - condition: needs_correction  # 需要修正 / Needs correction
        to: self_correction
      - condition: satisfactory  # 满意的结果 / Satisfactory result
        to: final_answer
# Logging Configuration / 日志配置
logging:
  level: INFO  # 日志级别 / Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  file: ./logs/grag.log  # 日志文件路径 / Log file path
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}'  # 日志格式 / Log format
  rotation: 1 day  # 日志轮转周期 / Log rotation period
  retention: 30 days  # 日志保留时间 / Log retention period
# Model Configuration / 模型配置
model:
  base_model: Qwen/Qwen3-8B  # 基础模型名称 / Base model name
  model_type: qwen3  # 模型类型 / Model type
  max_length: 8192  # 最大序列长度 / Maximum sequence length
  load_in_4bit: true  # 是否使用4位量化 / Whether to use 4-bit quantization
  # Generation Parameters / 生成参数
  temperature: 0.7  # 温度参数 / Temperature for generation (0.0-2.0)
  top_k: 50  # Top-K采样 / Top-K sampling
  top_p: 0.9  # Top-P采样 / Top-P (nucleus) sampling
  # LoRA Configuration / LoRA配置
  lora_config:
    r: 32  # LoRA秩 / LoRA rank
    lora_alpha: 64  # LoRA alpha参数 / LoRA alpha parameter
    lora_dropout: 0.0  # LoRA dropout率 / LoRA dropout rate
    bias: none  # 偏置设置 / Bias setting ("none", "all", "lora_only")
    target_modules:  # 目标模块 / Target modules for LoRA
    - q_proj  # 查询投影 / Query projection
    - k_proj  # 键投影 / Key projection
    - v_proj  # 值投影 / Value projection
    - o_proj  # 输出投影 / Output projection
    - gate_proj  # 门投影 / Gate projection
    - up_proj  # 上投影 / Up projection
    - down_proj  # 下投影 / Down projection
# Retrieval Configuration / 检索配置
retrieval:
  # Vector Database Configuration / 向量数据库配置
  vector_db:
    type: faiss  # 向量数据库类型 / Vector DB type (faiss)
    dimension: 1024  # 向量维度 / Vector dimension (Qwen3-Embedding-0.6B: 1024)
    index_type: IndexFlatIP  # FAISS索引类型 / FAISS index type (IndexFlatIP, IndexFlatL2, IndexIVFFlat, IndexHNSWFlat)
    save_path: ./data/embeddings/faiss_index  # 向量存储保存路径 / Vector store save path
    creation_batch_size: 4  # 向量存储创建时的批次大小 / Batch size for vector store creation (建议: 4-8, 内存不足时可调小)
    # GPU Acceleration Configuration / GPU加速配置
    use_gpu: auto  # GPU使用模式 / GPU usage mode ("auto": 自动检测, "cpu": 强制CPU, "gpu": 强制GPU)
    gpu_device: 0  # GPU设备ID / GPU device ID (当use_gpu为"gpu"时使用)

  # Embedding Model Configuration / 嵌入模型配置
  embedding:
    model_name: Qwen/Qwen3-Embedding-0.6B  # 嵌入模型名称 / Embedding model name (Qwen3嵌入模型, 支持100+语言)
    # model_name: BAAI/bge-large-zh-v1.5  # 备用选项 / Alternative option: 中文BGE大模型
    batch_size: 32  # 批处理大小 / Batch size for encoding (GPU内存不足时可调小)
    max_length: 8192  # 最大文本长度 / Maximum text length (Qwen3支持32K上下文，这里设置8K平衡性能)
    device: auto  # 设备选择 / Device selection ("auto", "cpu", "cuda")
    normalize_embeddings: true  # 是否归一化嵌入 / Whether to normalize embeddings
    # Qwen3 Specific Configuration / Qwen3特有配置
    use_instruction: true  # 是否使用指令提示 / Whether to use instruction prompting (可提升1-5%性能)
    instruction_template: 为这个句子生成表示以用于检索相关文章：  # 指令模板 / Instruction template (中文)
    # instruction_template: Given a web search query, retrieve relevant passages that answer the query  # 英文指令模板 / English instruction template

  # Search Configuration / 搜索配置
  search:
    top_k: 10  # 初始检索数量 / Initial retrieval count
    similarity_threshold: 0.7  # 相似度阈值 / Similarity threshold (0.0-1.0)
    adaptive_threshold: true  # 自适应阈值 / Adaptive threshold adjustment

  # Reranking Configuration / 重排序配置
  reranking:
    enabled: true  # 是否启用重排序 / Whether to enable reranking
    model_name: Qwen/Qwen3-Reranker-0.6B  # 重排序模型名称 / Reranker model name (Qwen3重排序模型, 支持100+语言)
    # model_name: BAAI/bge-reranker-large  # 备用选项 / Alternative option: BGE重排序模型
    top_k_rerank: 5  # 重排序后保留数量 / Number of documents to keep after reranking
    batch_size: 16  # 重排序批处理大小 / Reranking batch size (GPU内存不足时可调小)
    # Qwen3 Specific Configuration / Qwen3特有配置
    use_instruction: true  # 是否使用指令提示 / Whether to use instruction prompting
    instruction_template: Given a web search query, retrieve relevant passages that answer the query  # 指令模板 / Instruction template

  # Self-Correction Configuration / 自我修正配置
  self_correction:
    enabled: true  # 是否启用自我修正 / Whether to enable self-correction
    confidence_threshold: 0.8  # 置信度阈值 / Confidence threshold for triggering correction
    max_iterations: 3  # 最大修正迭代次数 / Maximum correction iterations
    strategies:  # 修正策略 / Correction strategies
    - query_expansion  # 查询扩展 / Query expansion
    - parameter_adjustment  # 参数调整 / Parameter adjustment
    - web_search_fallback  # 网络搜索回退 / Web search fallback
# Reward Models Configuration / 奖励模型配置
reward_models:
  # Medical Accuracy Reward / 医疗准确性奖励
  medical_accuracy:
    type: llm_based  # 奖励类型 / Reward type
    model_name: gpt-3.5-turbo  # 评估模型 / Evaluation model
    prompt_template: medical_accuracy_grader  # 提示模板 / Prompt template
    weight: 0.35  # 权重 / Weight in final reward

  # Relevance Reward / 相关性奖励
  relevance:
    type: embedding_similarity  # 基于嵌入相似性 / Based on embedding similarity
    model_name: BAAI/bge-large-zh-v1.5  # 嵌入模型 / Embedding model
    threshold: 0.7  # 相似性阈值 / Similarity threshold
    weight: 0.25  # 权重 / Weight in final reward

  # Safety Reward / 安全性奖励
  safety:
    type: rule_based  # 基于规则 / Rule-based
    weight: 0.25  # 权重 / Weight in final reward
    rules:  # 安全规则 / Safety rules
    - no_harmful_advice  # 无有害建议 / No harmful advice
    - medical_disclaimer  # 医疗免责声明 / Medical disclaimer
    - professional_referral  # 专业转诊 / Professional referral

  # Completeness Reward / 完整性奖励
  completeness:
    type: length_and_coverage  # 基于长度和覆盖度 / Based on length and coverage
    min_length: 50  # 最小长度 / Minimum length
    weight: 0.1  # 权重 / Weight in final reward
    coverage_keywords:  # 覆盖关键词 / Coverage keywords
    - 症状  # 症状 / Symptoms
    - 建议  # 建议 / Recommendations
    - 注意事项  # 注意事项 / Precautions

  # Format Compliance Reward / 格式合规性奖励
  format_compliance:
    type: regex_based  # 基于正则表达式 / Regex-based
    weight: 0.05  # 权重 / Weight in final reward
    required_sections:  # 必需章节 / Required sections
    - 分析  # 分析 / Analysis
    - 建议  # 建议 / Recommendations
# Training Configuration / 训练配置
training:
  # Supervised Fine-Tuning (SFT) / 监督微调
  sft:
    output_dir: ./models/sft  # 输出目录 / Output directory
    num_train_epochs: 3  # 训练轮数 / Number of training epochs
    learning_rate: 2e-4  # 学习率 / Learning rate
    per_device_train_batch_size: 2  # 每设备训练批次大小 / Training batch size per device
    per_device_eval_batch_size: 2  # 每设备评估批次大小 / Evaluation batch size per device
    gradient_accumulation_steps: 8  # 梯度累积步数 / Gradient accumulation steps
    max_length: 2048  # 最大序列长度 / Maximum sequence length
    # Optimization Settings / 优化设置
    optim: adamw_8bit  # 优化器 / Optimizer
    lr_scheduler_type: linear  # 学习率调度器 / Learning rate scheduler
    warmup_ratio: 0.05  # 预热比例 / Warmup ratio
    weight_decay: 0.01  # 权重衰减 / Weight decay
    # Logging and Saving / 日志和保存
    logging_steps: 10  # 日志记录步数 / Logging steps
    save_steps: 500  # 保存步数 / Save steps
    eval_steps: 500  # 评估步数 / Evaluation steps

  # Group Relative Policy Optimization (GRPO) / 群体相对策略优化
  grpo:
    output_dir: ./models/grpo  # 输出目录 / Output directory
    num_train_epochs: 1  # 训练轮数 / Number of training epochs
    learning_rate: 5e-6  # 学习率 / Learning rate (通常比SFT更小)
    per_device_train_batch_size: 1  # 每设备训练批次大小 / Training batch size per device
    # Generation Settings / 生成设置
    max_prompt_length: 1024  # 最大提示长度 / Maximum prompt length
    max_completion_length: 4096  # 最大完成长度 / Maximum completion length
    num_generations: 4  # 每个提示的生成数量 / Number of generations per prompt
    temperature: 1.0  # 生成温度 / Generation temperature
    top_p: 0.85  # Top-p采样 / Top-p sampling
    # Reward Functions / 奖励函数
    reward_functions:  # 使用的奖励函数 / Reward functions to use
    - medical_accuracy  # 医疗准确性 / Medical accuracy
    - relevance  # 相关性 / Relevance
    - safety  # 安全性 / Safety
    - completeness  # 完整性 / Completeness
    - format_compliance  # 格式合规性 / Format compliance
# Web Search Configuration / 网络搜索配置
web_search:
  enabled: true  # 是否启用网络搜索 / Whether to enable web search
  provider: tavily  # 搜索提供商 / Search provider ("tavily", "google", "bing")
  api_key: ${TAVILY_API_KEY}  # API密钥 / API key (从环境变量获取)
  max_results: 5  # 最大搜索结果数 / Maximum number of search results
  search_depth: advanced  # 搜索深度 / Search depth ("basic", "advanced")
  time_range: month  # 时间范围 / Time range ("day", "week", "month", "year")
  fallback_enabled: true  # 是否启用回退搜索 / Whether to enable fallback search
  # Trusted Domains / 可信域名白名单
  domains_whitelist:
  - baike.baidu.com  # 百度百科 / Baidu Encyclopedia
  - zh.wikipedia.org  # 中文维基百科 / Chinese Wikipedia
  - med.sina.com.cn  # 新浪医疗 / Sina Medical
