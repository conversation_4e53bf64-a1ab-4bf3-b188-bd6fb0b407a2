data:
  dataset_name: mi<PERSON><PERSON><PERSON><PERSON><PERSON>/ChatMed_Consult_Dataset
  max_samples: 1000
  preprocessing:
    clean_text: true
    max_length: 2048
    min_length: 10
    remove_duplicates: true
  test_split: 0.1
  train_split: 0.8
  val_split: 0.1
environment:
  compile_model: false
  deterministic: true
  device: auto
  mixed_precision: true
  seed: 3407
evaluation:
  human_eval:
    enabled: false
    evaluators:
    - medical_expert
    - general_user
    sample_size: 100
  medical_eval:
    criteria:
    - symptom_understanding
    - diagnosis_accuracy
    - treatment_appropriateness
    - risk_assessment
    enabled: true
  metrics:
  - rouge
  - bleu
  - bert_score
  - medical_accuracy
  - safety_score
  - relevance_score
  - retrieval_precision
  - retrieval_recall
frontend:
  description: Medical Consultation AI Assistant with GRPO-optimized RAG
  language: zh-CN
  max_history: 10
  show_confidence: true
  show_retrieval_details: true
  show_sources: true
  subtitle: 基于GRPO优化的医疗RAG系统
  theme: light
  title: GRAG - 医疗问诊智能助手
langgraph:
  edges:
    conditional_edges:
    - conditions:
      - condition: needs_web_search
        to: web_search
      - condition: standard_retrieval
        to: retrieval
      from: query_analysis
    - conditions:
      - condition: needs_correction
        to: self_correction
      - condition: satisfactory
        to: final_answer
      from: generation
  nodes:
  - query_analysis
  - retrieval
  - reranking
  - generation
  - self_correction
  - web_search
  - final_answer
logging:
  file: ./logs/grag.log
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}'
  level: INFO
  retention: 30 days
  rotation: 1 day
model:
  base_model: Qwen/Qwen3-8B
  load_in_4bit: true
  lora_config:
    bias: none
    lora_alpha: 64
    lora_dropout: 0.0
    r: 32
    target_modules:
    - q_proj
    - k_proj
    - v_proj
    - o_proj
    - gate_proj
    - up_proj
    - down_proj
  max_length: 8192
  model_type: qwen3
  temperature: 0.7
  top_k: 50
  top_p: 0.9
retrieval:
  embedding:
    batch_size: 32
    device: auto
    instruction_template: 为这个句子生成表示以用于检索相关文章：
    max_length: 8192
    model_name: Qwen/Qwen3-Embedding-0.6B
    normalize_embeddings: true
    use_instruction: true
  reranking:
    batch_size: 16
    enabled: true
    instruction_template: Given a web search query, retrieve relevant passages that
      answer the query
    model_name: Qwen/Qwen3-Reranker-0.6B
    top_k_rerank: 5
    use_instruction: true
  search:
    adaptive_threshold: true
    similarity_threshold: 0.7
    top_k: 10
  self_correction:
    confidence_threshold: 0.8
    enabled: true
    max_iterations: 3
    strategies:
    - query_expansion
    - parameter_adjustment
    - web_search_fallback
  vector_db:
    creation_batch_size: 4
    dimension: 1024
    gpu_device: 0
    index_type: IndexFlatIP
    save_path: ./data/embeddings/faiss_index
    type: faiss
    use_gpu: auto
reward_models:
  completeness:
    coverage_keywords:
    - 症状
    - 建议
    - 注意事项
    min_length: 50
    type: length_and_coverage
    weight: 0.1
  format_compliance:
    required_sections:
    - 分析
    - 建议
    type: regex_based
    weight: 0.05
  medical_accuracy:
    model_name: gpt-3.5-turbo
    prompt_template: medical_accuracy_grader
    type: llm_based
    weight: 0.35
  relevance:
    model_name: BAAI/bge-large-zh-v1.5
    threshold: 0.7
    type: embedding_similarity
    weight: 0.25
  safety:
    rules:
    - no_harmful_advice
    - medical_disclaimer
    - professional_referral
    type: rule_based
    weight: 0.25
training:
  grpo:
    learning_rate: 5e-6
    max_completion_length: 4096
    max_prompt_length: 1024
    num_generations: 4
    num_train_epochs: 1
    output_dir: ./models/grpo
    per_device_train_batch_size: 1
    reward_functions:
    - medical_accuracy
    - relevance
    - safety
    - completeness
    - format_compliance
    temperature: 1.0
    top_p: 0.85
  sft:
    eval_steps: 500
    gradient_accumulation_steps: 8
    learning_rate: 2e-4
    logging_steps: 10
    lr_scheduler_type: linear
    max_length: 2048
    num_train_epochs: 3
    optim: adamw_8bit
    output_dir: ./models/sft
    per_device_eval_batch_size: 2
    per_device_train_batch_size: 2
    save_steps: 500
    warmup_ratio: 0.05
    weight_decay: 0.01
web_search:
  api_key: ${TAVILY_API_KEY}
  domains_whitelist:
  - baike.baidu.com
  - zh.wikipedia.org
  - med.sina.com.cn
  enabled: true
  fallback_enabled: true
  max_results: 5
  provider: tavily
  search_depth: advanced
  time_range: month
