# GRAG System Configuration - 基于GRPO优化的医疗RAG系统配置
# GRAG System Configuration - Medical RAG System with GRPO Optimization

# Model Configuration / 模型配置
model:
  base_model: "Qwen/Qwen3-8B"  # 基础模型
  model_type: "qwen3"
  max_length: 8192  # 最大序列长度
  temperature: 0.7  # 生成温度
  top_p: 0.9
  top_k: 50
  load_in_4bit: true  # 4位量化以节省内存
  lora_config:  # LoRA配置
    r: 32  # LoRA rank
    lora_alpha: 64
    target_modules: ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    lora_dropout: 0.0
    bias: "none"

# Training Configuration / 训练配置
training:
  sft:  # Supervised Fine-Tuning / 监督微调
    output_dir: "./models/sft"
    num_train_epochs: 3
    per_device_train_batch_size: 2
    per_device_eval_batch_size: 2
    gradient_accumulation_steps: 8
    learning_rate: 2e-4  # 学习率
    warmup_ratio: 0.05
    logging_steps: 10
    save_steps: 500
    eval_steps: 500
    max_length: 2048
    optim: "adamw_8bit"  # 8位优化器
    weight_decay: 0.01
    lr_scheduler_type: "linear"

  grpo:  # Group Relative Policy Optimization / 群体相对策略优化
    output_dir: "./models/grpo"
    num_train_epochs: 1
    per_device_train_batch_size: 1
    learning_rate: 5e-6  # GRPO学习率
    num_generations: 4  # 生成数量
    temperature: 1.0
    top_p: 0.85
    max_completion_length: 4096
    max_prompt_length: 1024
    reward_functions:  # 奖励函数
      - "medical_accuracy"    # 医疗准确性
      - "relevance"          # 相关性
      - "safety"             # 安全性
      - "completeness"       # 完整性
      - "format_compliance"  # 格式合规性

# Data Configuration / 数据配置
data:
  dataset_name: "michaelwzhu/ChatMed_Consult_Dataset"  # 中文医疗问诊数据集
  train_split: 0.8
  val_split: 0.1
  test_split: 0.1
  max_samples: 1000  # 开发阶段样本限制
  preprocessing:  # 数据预处理
    remove_duplicates: true  # 去重
    min_length: 10  # 最小长度
    max_length: 2048  # 最大长度
    clean_text: true  # 文本清理
  
# Retrieval Configuration / 检索配置
retrieval:
  vector_db:  # 向量数据库
    type: "faiss"  # 使用FAISS
    dimension: 1024  # 向量维度
    index_type: "IndexFlatIP"  # 内积索引
    save_path: "./data/embeddings/faiss_index"
    creation_batch_size: 4  # 向量存储创建时的批次大小 (建议: 4-8, 内存不足时可调小)

  embedding:  # 嵌入模型
    model_name: "BAAI/bge-large-zh-v1.5"  # 中文BGE大模型
    batch_size: 32
    max_length: 512
    device: "auto"
    normalize_embeddings: true  # 归一化嵌入

  search:  # 搜索配置
    top_k: 10  # 初始检索数量
    similarity_threshold: 0.7  # 相似度阈值
    adaptive_threshold: true  # 自适应阈值

  reranking:  # 重排序
    enabled: true
    model_name: "BAAI/bge-reranker-large"  # 重排序模型
    top_k_rerank: 5  # 重排序后保留数量
    batch_size: 16

  self_correction:  # 自我修正机制
    enabled: true
    max_iterations: 3  # 最大修正次数
    confidence_threshold: 0.8  # 置信度阈值
    strategies:  # 修正策略
      - "query_expansion"      # 查询扩展
      - "parameter_adjustment" # 参数调整
      - "web_search_fallback" # 网络搜索回退

# Reward Models Configuration / 奖励模型配置
reward_models:
  medical_accuracy:  # 医疗准确性奖励
    type: "llm_based"  # 基于LLM的评估
    model_name: "gpt-3.5-turbo"
    weight: 0.35
    prompt_template: "medical_accuracy_grader"

  relevance:  # 相关性奖励
    type: "embedding_similarity"
    model_name: "BAAI/bge-large-zh-v1.5"
    weight: 0.25
    threshold: 0.7

  safety:  # 安全性奖励
    type: "rule_based"
    weight: 0.25
    rules:  # 安全规则
      - "no_harmful_advice"     # 无有害建议
      - "medical_disclaimer"    # 医疗免责声明
      - "professional_referral" # 专业转诊建议

  completeness:  # 完整性奖励
    type: "length_and_coverage"
    weight: 0.10
    min_length: 50
    coverage_keywords: ["症状", "建议", "注意事项"]

  format_compliance:  # 格式合规性奖励
    type: "regex_based"
    weight: 0.05
    required_sections: ["分析", "建议"]

# Web Search Configuration / 网络搜索配置
web_search:
  enabled: true  # 启用网络搜索
  provider: "tavily"  # 搜索提供商
  api_key: "${TAVILY_API_KEY}"
  max_results: 5  # 最大结果数
  search_depth: "advanced"  # 搜索深度
  time_range: "month"  # 时间范围
  domains_whitelist:  # 域名白名单
    - "baike.baidu.com"
    - "zh.wikipedia.org"
    - "med.sina.com.cn"
  fallback_enabled: true  # 启用回退机制

# Evaluation Configuration / 评估配置
evaluation:
  metrics:  # 评估指标
    - "rouge"           # ROUGE分数
    - "bleu"            # BLEU分数
    - "bert_score"      # BERTScore
    - "medical_accuracy" # 医疗准确性
    - "safety_score"    # 安全性评分
    - "relevance_score" # 相关性评分
    - "retrieval_precision" # 检索精确度
    - "retrieval_recall"    # 检索召回率

  medical_eval:  # 医疗专用评估
    enabled: true
    criteria:  # 评估标准
      - "symptom_understanding"  # 症状理解
      - "diagnosis_accuracy"     # 诊断准确性
      - "treatment_appropriateness" # 治疗适当性
      - "risk_assessment"        # 风险评估

  human_eval:  # 人工评估
    enabled: false
    sample_size: 100
    evaluators: ["medical_expert", "general_user"]

# LangGraph Workflow Configuration / LangGraph工作流配置
langgraph:
  nodes:  # 节点配置
    - "query_analysis"      # 查询分析
    - "retrieval"          # 检索
    - "reranking"          # 重排序
    - "generation"         # 生成
    - "self_correction"    # 自我修正
    - "web_search"         # 网络搜索
    - "final_answer"       # 最终答案

  edges:  # 边配置
    conditional_edges:  # 条件边
      - from: "query_analysis"
        conditions:
          - condition: "needs_web_search"
            to: "web_search"
          - condition: "standard_retrieval"
            to: "retrieval"
      - from: "generation"
        conditions:
          - condition: "needs_correction"
            to: "self_correction"
          - condition: "satisfactory"
            to: "final_answer"

# Frontend Configuration / 前端配置
frontend:
  title: "GRAG - 医疗问诊智能助手"  # 应用标题
  subtitle: "基于GRPO优化的医疗RAG系统"  # 副标题
  description: "Medical Consultation AI Assistant with GRPO-optimized RAG"
  max_history: 10  # 最大历史记录
  show_sources: true  # 显示来源
  show_confidence: true  # 显示置信度
  show_retrieval_details: true  # 显示检索详情
  theme: "light"  # 主题
  language: "zh-CN"  # 语言

# Logging Configuration / 日志配置
logging:
  level: "INFO"  # 日志级别
  file: "./logs/grag.log"  # 日志文件
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
  rotation: "1 day"  # 日志轮转
  retention: "30 days"  # 保留时间

# Environment / 环境配置
environment:
  device: "auto"  # 设备选择: auto, cpu, cuda
  mixed_precision: true  # 混合精度
  compile_model: false  # 模型编译
  seed: 3407  # 随机种子
  deterministic: true  # 确定性计算
