# Qwen3 Models Upgrade Guide / Qwen3模型升级指南

本指南将帮助您将GRAG项目从BGE模型升级到最新的Qwen3嵌入和重排序模型，并添加GPU加速支持。

## 🎯 升级概述 / Upgrade Overview

### 升级内容 / What's Being Upgraded

1. **嵌入模型 / Embedding Model**
   - 从: `BAAI/bge-large-zh-v1.5` 
   - 到: `Qwen/Qwen3-Embedding-0.6B`

2. **重排序模型 / Reranker Model**
   - 从: `BAAI/bge-reranker-large`
   - 到: `Qwen/Qwen3-Reranker-0.6B`

3. **FAISS支持 / FAISS Support**
   - 添加GPU加速支持
   - 自动检测和配置GPU/CPU

### 优势 / Benefits

- 🚀 **更好的性能**: Qwen3模型在多语言任务上表现更优
- 🌍 **多语言支持**: 支持100+种语言
- ⚡ **GPU加速**: 支持FAISS GPU加速，提升检索速度
- 🎯 **指令感知**: 支持自定义指令提示，提升检索精度
- 📏 **灵活维度**: 支持自定义嵌入维度（32-1024）

## 🛠️ 升级步骤 / Upgrade Steps

### 步骤1: 备份当前配置 / Step 1: Backup Current Configuration

```bash
# 备份当前配置文件
cp configs/config.yaml configs/config_backup_$(date +%Y%m%d_%H%M%S).yaml

# 备份当前向量存储（如果存在）
cp -r data/embeddings data/embeddings_backup_$(date +%Y%m%d_%H%M%S)
```

### 步骤2: 更新依赖 / Step 2: Update Dependencies

```bash
# 更新transformers版本（Qwen3需要>=4.51.0）
pip install transformers>=4.51.0

# 更新sentence-transformers版本
pip install sentence-transformers>=2.7.0
```

### 步骤3: 安装FAISS GPU支持 / Step 3: Install FAISS GPU Support

运行FAISS安装脚本，它会自动检测您的系统并推荐合适的版本：

```bash
python scripts/install_faiss.py
```

或者手动安装：

```bash
# 如果有CUDA 12.x
pip uninstall faiss-cpu faiss-gpu -y
pip install faiss-gpu-cu12>=1.11.0

# 如果有CUDA 11.x
pip uninstall faiss-cpu faiss-gpu -y
pip install faiss-gpu-cu11>=1.11.0

# 如果只有CPU
pip install faiss-cpu>=1.10.0
```

### 步骤4: 更新配置文件 / Step 4: Update Configuration

运行配置更新脚本：

```bash
python scripts/update_config_for_qwen3.py
```

或者手动更新 `configs/config.yaml`:

```yaml
retrieval:
  vector_db:
    type: "faiss"
    dimension: 1024  # Qwen3-Embedding-0.6B维度
    index_type: "IndexFlatIP"
    save_path: "./data/embeddings/faiss_index"
    creation_batch_size: 4
    # GPU加速配置
    use_gpu: "auto"  # "auto", "cpu", "gpu"
    gpu_device: 0

  embedding:
    model_name: "Qwen/Qwen3-Embedding-0.6B"
    batch_size: 32
    max_length: 8192  # Qwen3支持32K，这里设置8K平衡性能
    device: "auto"
    normalize_embeddings: true
    # Qwen3特有配置
    use_instruction: true
    instruction_template: "为这个句子生成表示以用于检索相关文章："

  reranking:
    enabled: true
    model_name: "Qwen/Qwen3-Reranker-0.6B"
    top_k_rerank: 5
    batch_size: 16
    # Qwen3特有配置
    use_instruction: true
    instruction_template: "Given a web search query, retrieve relevant passages that answer the query"
```

### 步骤5: 测试新模型 / Step 5: Test New Models

运行测试脚本验证升级是否成功：

```bash
python scripts/test_qwen3_models.py
```

### 步骤6: 重新创建向量存储 / Step 6: Recreate Vector Store

由于模型和维度发生了变化，需要重新创建向量存储：

```bash
# 删除旧的向量存储
rm -rf data/embeddings/faiss_index

# 重新创建向量存储
python scripts/02_create_vector_store.py
```

### 步骤7: 验证系统 / Step 7: Verify System

运行完整的系统测试：

```bash
python scripts/99_test_model.py
```

## 🔧 配置选项 / Configuration Options

### GPU配置 / GPU Configuration

```yaml
vector_db:
  use_gpu: "auto"  # 选项: "auto", "cpu", "gpu"
  gpu_device: 0    # GPU设备ID
```

- `"auto"`: 自动检测GPU可用性
- `"cpu"`: 强制使用CPU
- `"gpu"`: 强制使用GPU（需要GPU可用）

### 指令模板 / Instruction Templates

```yaml
embedding:
  use_instruction: true
  instruction_template: "为这个句子生成表示以用于检索相关文章："

reranking:
  use_instruction: true
  instruction_template: "Given a web search query, retrieve relevant passages that answer the query"
```

## 🐛 故障排除 / Troubleshooting

### 常见问题 / Common Issues

1. **KeyError: 'qwen3'**
   ```bash
   # 解决方案：更新transformers版本
   pip install transformers>=4.51.0
   ```

2. **CUDA out of memory**
   ```yaml
   # 解决方案：减少批次大小
   embedding:
     batch_size: 16  # 从32减少到16
   reranking:
     batch_size: 8   # 从16减少到8
   ```

3. **FAISS GPU not available**
   ```bash
   # 解决方案：安装正确的FAISS GPU版本
   python scripts/install_faiss.py
   ```

4. **模型下载失败**
   ```bash
   # 解决方案：使用镜像源
   export HF_ENDPOINT=https://hf-mirror.com
   python scripts/test_qwen3_models.py
   ```

### 性能优化 / Performance Optimization

1. **启用Flash Attention**
   ```bash
   pip install flash-attn>=2.3.0
   ```

2. **调整批次大小**
   - GPU内存充足：增加batch_size
   - GPU内存不足：减少batch_size

3. **使用混合精度**
   - 模型会自动使用fp16以节省内存

## 📊 性能对比 / Performance Comparison

| 指标 | BGE模型 | Qwen3模型 | 提升 |
|------|---------|-----------|------|
| 多语言支持 | 中文为主 | 100+语言 | ✅ |
| 嵌入维度 | 1024 | 1024 | - |
| 上下文长度 | 512 | 32K | ✅ |
| 指令感知 | 有限 | 完全支持 | ✅ |
| GPU加速 | 部分 | 完全支持 | ✅ |

## 🔄 回滚指南 / Rollback Guide

如果需要回滚到原来的BGE模型：

1. **恢复配置文件**
   ```bash
   cp configs/config_backup_YYYYMMDD_HHMMSS.yaml configs/config.yaml
   ```

2. **恢复向量存储**
   ```bash
   rm -rf data/embeddings
   cp -r data/embeddings_backup_YYYYMMDD_HHMMSS data/embeddings
   ```

3. **重新安装依赖**
   ```bash
   pip install transformers>=4.35.0  # 恢复到原版本
   ```

## 📞 支持 / Support

如果在升级过程中遇到问题：

1. 查看测试脚本输出：`python scripts/test_qwen3_models.py`
2. 检查日志文件：`logs/grag.log`
3. 参考故障排除部分
4. 提交Issue到项目仓库

## 🎉 升级完成 / Upgrade Complete

升级完成后，您的GRAG系统将具备：

- ✅ 最新的Qwen3嵌入和重排序模型
- ✅ GPU加速支持（如果可用）
- ✅ 更好的多语言支持
- ✅ 指令感知能力
- ✅ 优化的性能配置
- ✅ 完整的中英文配置注释

## 📁 升级文件清单 / Upgraded Files List

### 核心代码文件 / Core Code Files
- `src/retrieval/enhanced_vector_store.py` - 支持Qwen3嵌入模型和GPU加速
- `src/retrieval/reranker.py` - 支持Qwen3重排序模型
- `requirements.txt` - 更新依赖版本
- `configs/config.yaml` - 完整的配置文件（含详细注释）

### 辅助脚本 / Helper Scripts
- `scripts/install_faiss.py` - FAISS安装助手
- `scripts/update_config_for_qwen3.py` - 配置更新脚本
- `scripts/test_qwen3_models.py` - 模型测试脚本

### 文档 / Documentation
- `QWEN3_UPGRADE_GUIDE.md` - 详细升级指南

## 🚀 快速开始 / Quick Start

```bash
# 1. 安装FAISS
python scripts/install_faiss.py

# 2. 更新配置
python scripts/update_config_for_qwen3.py

# 3. 测试模型
python scripts/test_qwen3_models.py

# 4. 重建向量存储
python scripts/02_create_vector_store.py

# 5. 启动应用
python scripts/05_run_app.py
```

享受更强大的检索和生成能力！
