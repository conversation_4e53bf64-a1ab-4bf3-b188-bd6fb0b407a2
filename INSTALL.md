# GRAG Installation Guide / GRAG安装指南

## 🚀 Quick Start / 快速开始

### Option 1: Quick Installation (Recommended) / 选项1: 快速安装（推荐）

```bash
# Quick installation using requirements.txt
# 使用requirements.txt快速安装
python install_dependencies.py --quick
```

### Option 2: Full Installation / 选项2: 完整安装

```bash
# Full step-by-step installation with comprehensive testing
# 完整的分步安装，包含全面测试
python install_dependencies.py
```

### Option 3: Manual Installation / 选项3: 手动安装

```bash
# Install directly from requirements.txt
# 直接从requirements.txt安装
pip install -r requirements.txt

# For Chinese users (using mirror)
# 中国用户（使用镜像源）
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 🔧 Installation Options / 安装选项

### Basic Options / 基础选项

```bash
# CPU only installation
# 仅CPU安装
python install_dependencies.py --cpu-only

# Use specific mirror source
# 使用指定镜像源
python install_dependencies.py --mirror tsinghua

# Use official PyPI only
# 仅使用官方PyPI
python install_dependencies.py --no-mirror

# Skip Qwen3 compatibility test
# 跳过Qwen3兼容性测试
python install_dependencies.py --skip-qwen3-test
```

### Combined Options / 组合选项

```bash
# Quick CPU-only installation with mirror
# 快速CPU安装使用镜像源
python install_dependencies.py --quick --cpu-only --mirror tsinghua

# Full installation without testing
# 完整安装不进行测试
python install_dependencies.py --skip-qwen3-test --no-mirror
```

## 📋 System Requirements / 系统要求

- Python 3.8+
- pip (latest version recommended)
- 5GB+ free disk space
- Internet connection
- GPU with CUDA 11.x/12.x (optional, for GPU acceleration)

## 🎯 What Gets Installed / 安装内容

### Core Dependencies / 核心依赖
- **PyTorch** >=2.0.0 - Deep learning framework
- **Transformers** >=4.51.0 - HuggingFace Transformers (Qwen3 support)
- **Sentence-Transformers** >=2.7.0 - Sentence embeddings (Qwen3 compatible)
- **FAISS** - Vector similarity search (CPU/GPU auto-detected)

### LangChain Ecosystem / LangChain生态
- **LangChain** >=0.3.0 - LLM application framework
- **LangGraph** >=0.2.0 - Workflow orchestration
- **LangChain Community** - Community integrations

### Frontend & Utilities / 前端和工具
- **Streamlit** - Web application framework
- **Plotly** - Interactive visualizations
- **Pandas, NumPy** - Data processing
- **Loguru** - Logging

## 🧪 Verification / 验证安装

After installation, verify everything works:

```bash
# Test Qwen3 models compatibility
# 测试Qwen3模型兼容性
python scripts/test_qwen3_models.py

# Test basic imports
# 测试基础导入
python -c "import torch, transformers, sentence_transformers, faiss; print('✅ All packages imported successfully!')"
```

## 🐛 Troubleshooting / 故障排除

### Common Issues / 常见问题

1. **CUDA out of memory**
   ```bash
   # Use CPU-only installation
   python install_dependencies.py --cpu-only
   ```

2. **Network timeout**
   ```bash
   # Use mirror source
   python install_dependencies.py --mirror tsinghua
   ```

3. **Transformers version too old**
   ```bash
   # Upgrade transformers
   pip install transformers>=4.51.0 --upgrade
   ```

4. **FAISS GPU installation failed**
   ```bash
   # Check CUDA version and install appropriate FAISS
   nvidia-smi
   pip install faiss-gpu-cu12>=1.11.0  # For CUDA 12.x
   pip install faiss-gpu-cu11>=1.11.0  # For CUDA 11.x
   ```

## 📞 Getting Help / 获取帮助

If you encounter issues:

1. Check the error messages carefully
2. Try different installation options
3. Refer to the Qwen3 upgrade guide: `QWEN3_UPGRADE_GUIDE.md`
4. Submit an issue to the project repository

## 🎉 Next Steps / 下一步

After successful installation:

1. **Configure the system**: `python scripts/update_config_for_qwen3.py`
2. **Create vector store**: `python scripts/02_create_vector_store.py`
3. **Run the application**: `python scripts/05_run_app.py`

Enjoy using GRAG with Qwen3 models! 🚀
